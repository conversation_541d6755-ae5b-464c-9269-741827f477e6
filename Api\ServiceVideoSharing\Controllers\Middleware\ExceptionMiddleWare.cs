using BLL.SysService;
using Common.Exceptions;
using Common.Https;
using Entity.Dto;
using Microsoft.Extensions.DependencyInjection;
using ServiceVideoSharing.Controllers.BasisController.ResuItEntity;
using System.Security.Claims;
using System.Text.Json;

namespace ServiceVideoSharing.Controllers.Middleware
{
    /// <summary>
    /// 全局异常处理中间件
    /// 用于捕获和处理管道中未被其他过滤器处理的异常
    /// </summary>
    /// <param name="next">请求处理委托</param>
    /// <param name="logger">ILogger实例</param>
    /// <param name="environment">Web主机环境</param>
    /// <param name="serviceScopeFactory">服务作用域工厂</param>
    public class ExceptionMiddleWare(
        RequestDelegate next,
        ILogger<ExceptionMiddleWare> logger,
        IWebHostEnvironment environment,
        IServiceScopeFactory serviceScopeFactory)
    {
        private readonly RequestDelegate _next = next;
        private readonly ILogger<ExceptionMiddleWare> _logger = logger;
        private readonly bool _isDevelopment = environment.IsDevelopment();
        private readonly IServiceScopeFactory _serviceScopeFactory = serviceScopeFactory;

        /// <summary>
        /// 处理HTTP请求
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <returns>异步任务</returns>
        public async Task InvokeAsync(HttpContext context)
        {
            try
            {
                await _next(context);
            }
            catch (Exception ex)
            {
                await HandleExceptionAsync(context, ex);
            }
        }

        /// <summary>
        ///  处理异常
        /// </summary>
        /// <param name="context">HTTP上下文</param>
        /// <param name="ex">异常对象</param>
        /// <returns></returns>
        private async Task HandleExceptionAsync(HttpContext context, Exception ex)
        {
            // 获取异常详细信息
            string message = GetFullExceptionMessage(ex);
            string stackTrace = ex.StackTrace ?? string.Empty;
            string exceptionType = ex.GetType().Name;

            // 获取请求信息
            string path = context.Request.Path;
            string method = context.Request.Method;
            string queryString = context.Request.QueryString.ToString();

            // 尝试获取IP地址，使用更安全的方法
            string ip = GetSafeIpAddress(context);

            // 获取用户信息
            var userId = context.User?.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? string.Empty;
            var username = context.User?.FindFirst(ClaimTypes.Name)?.Value ?? string.Empty;

            // 确定HTTP状态码、响应码和用户消息，以及是否需要记录文本和数据库日志
            (int httpStatusCode, int responseCode, string userMessage, bool isLog) = DetermineStatusAndMessage(ex);

            // 如果需要记录文本和数据库日志
            if (isLog)
            {
                //记录数据库日志和文件日志 不等待日志记录完成
                _ = Task.Run(async () =>
                {
                    await WriteLogAsync(new CreateLogDto
                    {
                        UserId = userId,
                        Username = username,
                        Operation = "系统异常",
                        Method = method,
                        Params = JsonSerializer.Serialize(new { Path = path, Method = method, QueryString = queryString }),
                        Time = 0,
                        Ip = ip,
                        Path = path,
                        LogType = "System",
                        LogLevel = LogLevel.Error.ToString(),
                        Message = $"请求处理失败: {path}",
                        Exception = $"类型: {exceptionType}, 消息: {message}, 堆栈: {stackTrace}"
                    }, ex);
                });
            }


            // 返回JSON错误响应
            context.Response.StatusCode = httpStatusCode; // 根据异常类型设置HTTP状态码
            context.Response.ContentType = "application/json";

            // 使用Result类格式确保响应格式一致
            var result = new Result
            {
                Code = responseCode,
                Success = false,
                Msg = _isDevelopment ? message : userMessage
            };

            await context.Response.WriteAsJsonAsync(result);
        }


        /// <summary>
        /// 写入文本日志和数据库日志
        /// </summary>
        /// <param name="logDto">日志信息</param>
        /// <param name="ex">异常信息</param>
        /// <returns></returns>
        private async Task WriteLogAsync(CreateLogDto logDto, Exception ex)
        {
            try
            {
                // 创建新的服务作用域来避免生命周期问题
                using var scope = _serviceScopeFactory.CreateScope();
                var logService = scope.ServiceProvider.GetService<SysLogService>();
                if (logService != null)
                {
                    // 记录数据库日志
                    await logService.CreateAsync(logDto);
                }

                // 同时使用ILogger记录
                _logger.LogError(ex, "请求处理异常: {Path}, IP: {IP}", logDto.Path, logDto.Ip);
            }
            catch (Exception logEx)
            {
                _logger.LogError(logEx, "日志记录失败: {LogExMessage}, 原始异常: {ExMessage}", logEx.Message, ex.Message);
            }
        }

        /// <summary>
        /// 确定HTTP状态码、响应码和消息，返回是否需要记录文本和数据库日志
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <returns>HTTP状态码, 响应码, 消息, 是否需要记录文本和数据库日志</returns>
        private static (int httpStatusCode, int responseCode, string message, bool isLog) DetermineStatusAndMessage(Exception ex)
        => ex switch
        {
            // 授权异常 - HTTP 401，响应体code 401，不记录详细日志
            AuthorizationException => (401, ErrorCodes.UNAUTHORIZED, ex.Message, false),
            // 业务异常 - HTTP 200，响应体code 500，不记录详细日志
            BusinessException => (200, ErrorCodes.ERROR, ex.Message, false),
            // 其他系统异常 - HTTP 500，响应体code 500，记录详细日志
            _ => (500, ErrorCodes.ERROR, "服务器内部错误", true)
        };


        // 安全获取IP地址
        private static string GetSafeIpAddress(HttpContext context)
        {
            try
            {
                return context.GetIPAddress();
            }
            catch
            {
                return "无法获取IP";
            }
        }

        /// <summary>
        /// 递归获取异常及其内部异常的完整消息
        /// </summary>
        /// <param name="ex">异常对象</param>
        /// <returns>完整的异常消息</returns>
        private static string GetFullExceptionMessage(Exception ex)
        {
            var messages = new List<string>();
            var currentEx = ex;

            // 收集异常链中的所有消息
            while (currentEx != null)
            {
                messages.Add(currentEx.Message);
                currentEx = currentEx.InnerException;
            }

            // 合并所有异常消息
            return string.Join(" -> ", messages);
        }
    }

    /// <summary>
    /// 异常中间件扩展方法
    /// </summary>
    public static class ExceptionMiddlewareExtensions
    {
        /// <summary>
        /// 使用全局异常处理中间件
        /// </summary>
        /// <param name="builder">应用构建器</param>
        /// <returns>应用构建器</returns>
        public static IApplicationBuilder UseExceptionMiddleware(this IApplicationBuilder builder)
        => builder.UseMiddleware<ExceptionMiddleWare>();
    }
}
