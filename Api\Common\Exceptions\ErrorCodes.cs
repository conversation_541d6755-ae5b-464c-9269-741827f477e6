namespace Common.Exceptions
{
    /// <summary>
    /// 错误码常量类
    /// 简化的错误码设计：200正常，500错误，401没有权限
    /// </summary>
    public static class ErrorCodes
    {
        /// <summary>
        /// 成功
        /// </summary>
        public const int SUCCESS = 200;

        /// <summary>
        /// 未授权访问
        /// </summary>
        public const int UNAUTHORIZED = 401;

        /// <summary>
        /// 业务逻辑错误
        /// </summary>
        public const int ERROR = 500;

        /// <summary>
        /// 错误码与消息的映射字典
        /// </summary>
        public static readonly Dictionary<int, string> ErrorMessages = new()
        {
            { SUCCESS, "操作成功" },
            { UNAUTHORIZED, "未授权访问" },
            { ERROR, "操作失败" }
        };

        /// <summary>
        /// 获取错误码对应的消息
        /// </summary>
        /// <param name="errorCode">错误码</param>
        /// <returns>错误消息</returns>
        public static string GetMessage(int errorCode)
        {
            return ErrorMessages.TryGetValue(errorCode, out var message) ? message : "未知错误";
        }
    }
}
