using Autofac;
using Common;
using Common.Autofac;
using Common.Export;

namespace ServiceVideoSharing.Infrastructure
{
    /// <summary>
    /// Autofac模块
    /// </summary>
    public class AutofacModule : Autofac.Module
    {
        /// <summary>
        /// 加载Autofac模块
        /// </summary>
        /// <param name="builder">容器构建器</param>
        protected override void Load(ContainerBuilder builder)
        {
            try
            {
                // 获取所有非动态程序集
                var assemblies = AppDomain.CurrentDomain.GetAssemblies()
                    .Where(a => !a.IsDynamic &&
                               a.FullName != null &&
                               !a.FullName.StartsWith("System.") &&
                               !a.FullName.StartsWith("Microsoft."))
                    .ToArray();

                // DbContext现在通过标准ASP.NET Core方式注册，不需要在Autofac中重复注册

                //// 注册仓储
                //builder.RegisterGeneric(typeof(Repository<>))
                //    .As(typeof(IRepository<>))
                //    .InstancePerLifetimeScope();

                // 注册导出服务
                builder.RegisterType<ExportService>()
                    .As<IExportService>()
                    .InstancePerLifetimeScope();

                // 注册导入服务
                builder.RegisterType<ImportService>()
                    .As<IImportService>()
                    .InstancePerLifetimeScope();

                // 注册HttpClientFactory（从.NET Core DI容器中获取）
                builder.Register(c => c.Resolve<IServiceProvider>().GetRequiredService<IHttpClientFactory>())
                    .As<IHttpClientFactory>()
                    .InstancePerLifetimeScope();

                // 注册ILogger<T>（从.NET Core DI容器中获取）
                builder.RegisterGeneric(typeof(Microsoft.Extensions.Logging.Logger<>))
                    .As(typeof(ILogger<>))
                    .InstancePerLifetimeScope();





                // 移除手动注册逻辑，统一使用特性注册和约定注册

                // 注册配置
                builder.Register(c =>
                {
                    var config = c.Resolve<IConfiguration>()
                        .GetSection("BasisConfig")
                        .Get<BasisConfigDto>();
                    return config ?? throw new InvalidOperationException("BasisConfig not found in configuration.");
                })
                .AsSelf()
                .SingleInstance();

                // 使用约定注册
                builder.RegisterByConvention(assemblies);

                // 使用特性注册
                builder.RegisterByAttribute(assemblies);

                base.Load(builder);
            }
            catch (Exception)
            {
                // 记录错误并重新抛出异常
                throw;
            }
        }
    }
}